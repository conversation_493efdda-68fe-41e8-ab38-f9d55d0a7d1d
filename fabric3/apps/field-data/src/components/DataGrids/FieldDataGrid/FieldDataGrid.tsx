import React from "react";
import {
  GKDataGrid,
  GKDataGridInstance,
  GKIconName,
  GKTooltip,
  IRowIndicator,
} from "@granular/gds";
import "./Columns/style.css";
import { useFieldDataFeatureFlags } from "@granular/fabric3-library-activities-components/src/hooks/useFieldDataFeatureFlags";
import { GKIcon } from "@granular/gds";
import { FormattedMessage } from "react-intl";
import { FieldListData } from "../../../models/activitiesTable";
import { EmptyFields } from "../../EmptyViews/EmptyFields";
import { EmptyResults } from "../../EmptyViews/EmptyResults";
import { useFieldDataGrid } from "./useFieldDataGrid";
import { createRowProps } from "./helpers";

type FieldDataGriProps = {
  isLoading: boolean;
  tableInstance: React.MutableRefObject<GKDataGridInstance<FieldListData> | null>;
};

export const FieldDataGrid: React.FC<FieldDataGriProps> = React.memo(
  ({ isLoading, tableInstance }) => {
    const {
      columns,
      searchResults,
      expanderHeader,
      expanderTitle,
      expanderWidth,
      rowDetail,
      defaultSorted,
      onSortChange,
      onExpandChange,
      onSelectRows,
      defaultExpanded,
      areFieldsEmpty,
      areResultsEmpty,
      hideFieldListDataGridCheckboxes,
      onClick,
      didUserScroll,
    } = useFieldDataGrid({ isLoading, tableInstance });
    const { fieldDataThumbnailColors } = useFieldDataFeatureFlags();
    const myRef = React.useRef<HTMLButtonElement | null>(null);

    const rowProps = React.useMemo(
      () => createRowProps(fieldDataThumbnailColors),
      [fieldDataThumbnailColors]
    );

    const defaultSelected = React.useMemo(() => {
      // Workaround to maintain checkbox states
      const newDefaults: IRowIndicator = {};
      const selectedRows =
        tableInstance.current?.rows.filter((row) => row.isSelected) ?? [];
      for (const id of searchResults
        .map((row, index) =>
          selectedRows.some(
            (selectedRow) => selectedRow.original.uniqueId === row.uniqueId
          )
            ? index
            : -1
        )
        .filter((f) => f >= 0)) {
        newDefaults[id] = true;
      }
      return newDefaults;
    }, [searchResults, tableInstance]);

    const renderContent = () => {
      if (areFieldsEmpty) {
        return <EmptyFields />;
      }
      if (areResultsEmpty) {
        return <EmptyResults />;
      }
      return;
    };

    if (isLoading) {
      return (
        <GKDataGrid
          columns={columns}
          data={searchResults}
          isLoading={true}
          onSelection={onSelectRows}
          ref={tableInstance}
          gridLines="horizontal"
        />
      );
    }

    return (
      <>
        <GKDataGrid
          id={"fieldListId"}
          columns={columns}
          data={searchResults}
          data-qa="FieldList_DataGrid"
          expanderHeader={expanderHeader}
          expanderTitle={expanderTitle}
          expanderWidth={expanderWidth}
          onSelection={
            hideFieldListDataGridCheckboxes ? undefined : onSelectRows
          }
          rowDetail={rowDetail}
          rowProps={rowProps}
          ref={tableInstance}
          className="field-table"
          virtualize={!areResultsEmpty && !areFieldsEmpty}
          defaultVirtualRowHeight={62}
          defaultSorted={defaultSorted}
          onSortChange={onSortChange}
          defaultSelected={defaultSelected}
          onExpandChange={onExpandChange}
          defaultExpanded={defaultExpanded}
        />
        {renderContent()}
        {didUserScroll && (
          <>
            <button onClick={onClick} className="scrollToTop" ref={myRef}>
              <GKIcon
                className="scrollToTopIcon"
                name={GKIconName.ArrowUpward}
              />
            </button>
            <GKTooltip className="tooltip" target={myRef}>
              <FormattedMessage id="scroll_to_top" />
            </GKTooltip>
          </>
        )}
      </>
    );
  }
);
FieldDataGrid.displayName = "FieldDataGrid";
