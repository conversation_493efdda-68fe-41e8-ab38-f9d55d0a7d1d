import React from "react";
import {
  DataGridColumn,
  DataGridRow,
  DataGridSortingRule,
  GKDataGridInstance,
  IGKDataGridProps,
  IRowIndicator,
  useDeviceSizes,
} from "@granular/gds";
import { useIntl } from "react-intl";
import { isEmpty } from "lodash-es";
import { SetStateAction, useAtom, useAtomValue, useSetAtom } from "jotai";
import { FieldDataQueryParams } from "@granular/fabric3-library-activities-components/src/types/shared";
import { useFieldDataSearchParams } from "@granular/fabric3-library-activities-components/src/hooks/useFieldDataSearchParams";
import { LoadingFieldIdsStateAtom } from "@granular/fabric3-library-activities-components/src/state/loadingData";
import { useOperationIds } from "@granular/fabric3-library-activities-components/src/hooks/useOperationIds";
import { useFieldDataFeatureFlags } from "@granular/fabric3-library-activities-components/src/hooks/useFieldDataFeatureFlags";
import { segment } from "@granular/fabric3-core";
import { HorizontalVerticalToggleOptions } from "@granular/fabric3-library-activities-components";
import { useActivitySummaries } from "@granular/fabric3-library-activities-components/src/hooks/useActivitySummaries";
import { getInputIdsFromSummaries } from "@granular/fabric3-library-activities-components/src/hooks/loadFromRemote/loadSummariesFromRemote";
import { useFetchInputs } from "@granular/fabric3-library-activities-components/src/hooks/useFetchInputs";
import { INPUT_DATA_TYPES } from "@granular/fabric3-library-activities-components";
import {
  ActiveListStateAtom,
  ActiveScrollDirectionStateAtom,
  EditColumnsStateAtom,
  ExpandedUniqueRowIdsAtom,
  TableListSortAtom,
} from "../../../state/activitiesTable";
import { FieldListData } from "../../../models/activitiesTable";
import { SelectedFieldRowsAtom } from "../../../state/selectedRows";
import { useFieldListTableDataFilters } from "../../../hooks/useFieldListDataFilters";
import { getColumns, getExpanderHeader, getExpanderTitle } from "./helpers";
import { FieldListDetails } from "./components/FieldListDetails/FieldListDetails";

type useFieldDataGridProp = {
  isLoading: boolean;
  tableInstance: React.MutableRefObject<GKDataGridInstance<FieldListData> | null>;
};

type SetAtom<Args extends [SetStateAction<FieldListData[]>], Result> = (
  ...args: Args
) => Result;

export type UseFieldDataGridResult = {
  searchResults: FieldListData[];
  expanderHeader: ((isAllRowsExpanded: boolean) => string) | undefined;
  expanderTitle: (row: DataGridRow<FieldListData>) => string;
  expanderWidth: number;
  rowDetail: ((row: DataGridRow<FieldListData>) => React.ReactNode) | undefined;
  defaultSorted: Array<DataGridSortingRule<FieldListData>>;
  onSortChange: (result: DataGridSortingRule<Record<string, boolean>>) => void;
  onExpandChange: (expanded: IRowIndicator) => void;
  onSelectRows: SetAtom<[SetStateAction<FieldListData[]>], void>;
  defaultExpanded: IRowIndicator;
  areFieldsEmpty: boolean;
  areResultsEmpty: boolean;
  columns: Array<DataGridColumn<FieldListData>>;
  hideFieldListDataGridCheckboxes: boolean;
  onClick: () => void;
  didUserScroll: boolean
};

export type UseFieldDataGridHook = (
  prop: useFieldDataGridProp,
) => UseFieldDataGridResult; // props: UseFieldDataGridProps,

export const useFieldDataGrid: UseFieldDataGridHook = ({
  isLoading,
  tableInstance,
}) => {
  const [, setActiveToggleOption] = useAtom(ActiveScrollDirectionStateAtom);

  const [editColumnsSelectedOptions] = useAtom(EditColumnsStateAtom);
  const [FieldListSort, setFieldListSort] = useAtom(TableListSortAtom);
  const onSelectRows = useSetAtom(SelectedFieldRowsAtom);
  const [expandedUniqueRowIds, setExpandedUniqueRowIds] = useAtom(
    ExpandedUniqueRowIdsAtom,
  );

  const loadingFieldIdsState = useAtomValue(LoadingFieldIdsStateAtom);
  const activeView = useAtomValue(ActiveListStateAtom);

  const [{ businessCycleId }] =
    useFieldDataSearchParams<FieldDataQueryParams>();
  const operationIds = useOperationIds();
  const intl = useIntl();
  const { hideFieldListDataGridCheckboxes } = useFieldDataFeatureFlags();
  const { isMobileSized } = useDeviceSizes();
  const locale = useIntl().locale;
  const [{ businessCycleId: selectedBusinessCycleId }] =
    useFieldDataSearchParams<FieldDataQueryParams>();
  const { data: activitySummaries } = useActivitySummaries(
    selectedBusinessCycleId,
  );
  const summaryByActivityId = React.useMemo(
    () => activitySummaries?.summaryByActivityId ?? {},
    [activitySummaries],
  );
  const inputIds = React.useMemo(
    () => getInputIdsFromSummaries(summaryByActivityId),
    [summaryByActivityId],
  );
  const { data: inputsById } = useFetchInputs(inputIds);
  const mixListIds: string[] = [];

  const mixtures = Object.values(inputsById ?? []).filter((x) =>
    [INPUT_DATA_TYPES.MIXTURE, INPUT_DATA_TYPES.CUSTOM_MIXTURE].includes(
      x?.data_type,
    ),
  );
  if (mixtures) {
    for (const mix of mixtures) {
      const components = mix.details?.components;
      if (components) {
        for (const component of components) {
          mixListIds.push(component.input_id);
        }
      }
    }
  }
  const { data: mixtureComponentInputsById } = useFetchInputs(mixListIds);
  const {
    isLoading: isFieldListDataLoading,
    data: { originalData, searchResults },
  } = useFieldListTableDataFilters(mixtureComponentInputsById);

  const loadingFieldIds = React.useMemo(
    () => loadingFieldIdsState,
    [loadingFieldIdsState],
  );

  const hasMoreThanThreeActivities = searchResults
    .map((row) => row.activitySummaries?.length || 0)
    .some((length) => length > 3);

  React.useEffect(() => {
    if (isMobileSized || hasMoreThanThreeActivities) {
      setActiveToggleOption(HorizontalVerticalToggleOptions.HORIZONTAL);
    }
    if (!hasMoreThanThreeActivities) {
      setActiveToggleOption(HorizontalVerticalToggleOptions.VERTICAL);
    }
  }, [
    isMobileSized,
    setActiveToggleOption,
    businessCycleId,
    hasMoreThanThreeActivities,
  ]);

  const rowDetail: IGKDataGridProps<FieldListData>["rowDetail"] = ({
    toggleRowExpanded,
    original: {
      activitySummaries,
      contribution,
      cropAssignmentId,
      cropId,
      businessCycleId,
      fieldId,
      fieldRevisionId,
      area,
      hasYieldGoal,
      plantingActivitySummaries,
      seedNames,
      yieldGoal,
      irrigatedYieldGoal,
      drylandYieldGoal,
      yieldGoalUnits,
      hasYieldCompleted,
      yieldCompleted,
    },
  }) => {
    return (
      <FieldListDetails
        activitySummaries={activitySummaries}
        contribution={contribution}
        cropAssignmentId={cropAssignmentId}
        cropId={cropId}
        businessCycleId={businessCycleId}
        fieldId={fieldId}
        fieldRevisionId={fieldRevisionId}
        area={area}
        hasYieldGoal={hasYieldGoal}
        plantingActivitySummaries={plantingActivitySummaries}
        seedNames={seedNames}
        yieldGoal={yieldGoal}
        irrigatedYieldGoal={irrigatedYieldGoal}
        drylandYieldGoal={drylandYieldGoal}
        yieldGoalUnits={yieldGoalUnits}
        hasYieldCompleted={hasYieldCompleted}
        yieldCompleted={yieldCompleted}
        toggleRowExpanded={toggleRowExpanded}
      />
    );
  };

  const expanderHeader: IGKDataGridProps<FieldListData>["expanderHeader"] =
    React.useCallback(
      (allExpanded: boolean) => {
        return getExpanderHeader(intl, isMobileSized, allExpanded);
      },
      [isMobileSized, intl],
    );

  const expanderTitle = React.useMemo(
    () => (row: DataGridRow<FieldListData>) => {
      return getExpanderTitle(intl, isMobileSized, row, loadingFieldIds);
    },

    [intl, isMobileSized, loadingFieldIds],
  );

  const expanderWidth = isMobileSized ? 50 : 150;

  const onExpandChange = React.useCallback(
    (expanded: IRowIndicator) => {
      const expandedRowIds: string[] = [];
      for (const [key, value] of Object.entries(expanded)) {
        value && expandedRowIds.push(key);
      }
      const expandedRowUniqueIds: string[] = [];
      for (const [index, row] of searchResults.entries()) {
        if (expandedRowIds.includes(`${index}`)) {
          expandedRowUniqueIds.push(row.uniqueId!);
        }
      }
      // send a click 'Expand All/Collapse All' event to segment.
      // Only track if all rows are expanded or all are collapsed
      if (
        expandedRowIds.length === searchResults.length ||
        expandedRowIds.length === 0
      ) {
        segment.track(
          "click 'Expand All/Collapse All' rows within FieldDataGrid - Field Data",
          {
            action:
              expandedRowIds.length === searchResults.length
                ? "expand_all"
                : "collapse_all",
          },
        );
      }

      setExpandedUniqueRowIds(expandedRowUniqueIds);
    },
    [searchResults, setExpandedUniqueRowIds],
  );

  const onSortChange = React.useCallback(
    (result: DataGridSortingRule<Record<string, boolean>>) => {
      if (
        result.id !== FieldListSort[0]!.id ||
        result.desc !== FieldListSort[0]!.desc
      ) {
        setFieldListSort([result]);
        const key = `FieldTableScroll_${operationIds[0]}_${businessCycleId}`;
        sessionStorage.removeItem(key);
        // tableInstance.current?.virtualList?.scrollToItem(0, "start");
      }
    },
    [
      setFieldListSort,
      operationIds,
      businessCycleId,
      FieldListSort,
      // tableInstance,
    ],
  );

  const defaultExpanded = React.useMemo(() => {
    const expandedRowIdStatus: IRowIndicator = {};
    for (const [index, row] of searchResults.entries()) {
      if (expandedUniqueRowIds?.includes(row.uniqueId!)) {
        expandedRowIdStatus[`${index}`] = true;
      }
    }

    return expandedRowIdStatus;
  }, [searchResults, expandedUniqueRowIds]);

  const areFieldsEmpty = React.useMemo(
    () => isEmpty(originalData) && !isLoading,
    [isLoading, originalData],
  );

  const areResultsEmpty = React.useMemo(
    () => isEmpty(searchResults) && !isLoading && !isFieldListDataLoading,
    [searchResults, isFieldListDataLoading, isLoading],
  );

  // const debounceSaveFirstRowId = React.useRef(
  //   debounce((operationId: string, businessCycleId: string) => {
  //     const rows: [] | HTMLCollectionOf<Element> =
  //       document
  //         .querySelector("fieldTableId")
  //         ?.getElementsByClassName("tr-group") ?? [];
  //     let observer: IntersectionObserver;
  //     if (rows?.length > 0) {
  //       let firstRowId: string;
  //       observer = new IntersectionObserver((entries) => {
  //         for (let i = entries?.length - 1; i >= 0; i--) {
  //           if (entries[i]?.isIntersecting) {
  //             firstRowId = entries[
  //               i
  //             ]!.target.children[0]!.children[0]!.children[0]!.getAttribute(
  //               "id"
  //             )!
  //               .match(/\d/g)!
  //               .join("");
  //           }
  //           observer?.unobserve(entries[i]!.target);
  //         }
  //         const key = `FieldTableScroll_${operationId}_${businessCycleId}`;
  //         sessionStorage.setItem(key, `${firstRowId}`);
  //       }, {});
  //       for (const row of rows) {
  //         observer.observe(row);
  //       }
  //     }
  //   }, 500)
  // ).current;

  // React.useEffect(() => {
  //   if (!isLoading) {
  //     const table = document
  //       .querySelector("fieldTableId")
  //       ?.getElementsByClassName("tbody")[0];
  //     const scrollHandler = () => {
  //       debounceSaveFirstRowId(operationIds![0]!, businessCycleId!);
  //     };
  //     table?.addEventListener("scroll", scrollHandler);
  //     return () => table?.removeEventListener("scroll", scrollHandler);
  //   }
  // }, [
  //   isLoading,
  //   debounceSaveFirstRowId,
  //   gridData?.length,
  //   operationIds,
  //   businessCycleId,
  // ]);

  // const debounceGetFirstRowId = React.useRef(
  //   debounce((operationId: string, activeBusinessCycleId: string) => {
  //     const key = `FieldTableScroll_${operationId}_${activeBusinessCycleId}`;
  //     const id: string = sessionStorage.getItem(key)!;
  //     const rowIdx = tableInstance.current!.rows.findIndex((row) => {
  //       return row.id === id;
  //     });
  //     tableInstance.current?.virtualList?.scrollToItem(rowIdx, "start");
  //     firstScrollJump.current = false;
  //   }, 1000)
  // ).current;

  // const firstScrollJump = React.useRef(true);

  // React.useEffect(() => {
  //   const table = document
  //     .querySelector("fieldTableId")
  //     ?.getElementsByClassName("tbody")[0];

  //   if (table && firstScrollJump.current) {
  //     debounceGetFirstRowId(operationIds![0]!, businessCycleId!);
  //   }
  // });

  // React.useEffect(() => {
  //   return () => {
  //     debounceSaveFirstRowId.cancel();
  //     debounceGetFirstRowId.cancel();
  //   };
  // }, [debounceSaveFirstRowId, debounceGetFirstRowId]);

  const columns: Array<DataGridColumn<FieldListData>> = React.useMemo(
    () =>
      getColumns(locale, editColumnsSelectedOptions[activeView], isMobileSized),
    [locale, editColumnsSelectedOptions, activeView, isMobileSized],
  );

  const onClick = React.useCallback(() => {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access
    tableInstance.current?.virtualList?.scrollToItem(0, "start");
  }, [tableInstance]);

  const didUserScroll = 

  return {
    columns,
    searchResults,
    expanderHeader,
    expanderTitle,
    expanderWidth,
    rowDetail,
    onSelectRows,
    defaultSorted: FieldListSort,
    onSortChange,
    onExpandChange,
    defaultExpanded,
    areFieldsEmpty,
    areResultsEmpty,
    hideFieldListDataGridCheckboxes,
    onClick,
    didUserScroll
  };
};
