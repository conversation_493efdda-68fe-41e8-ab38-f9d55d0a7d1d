import React, { ReactNode } from "react";
import {
  GKContent,
  GKDataGrid,
  GKDataGridInstance,
  GKIcon,
  GKIconName,
  GKLayoutContainer,
  GKTooltip,
  IRowIndicator,
} from "@granular/gds";
import { isEmpty } from "lodash-es";
import { useSetAtom } from "jotai";
import { ActivityListData } from "../../../models/activitiesTable";
import { EmptyActivities } from "../../EmptyViews/EmptyActivities";
import { SelectedActivityRowsAtom } from "../../../state/selectedRows";
import { useActivityDataGrid } from "./useActivityDataGrid";
import { FormattedMessage } from "react-intl";

export type ActivityDataGridProps = {
  className?: string;
  isLoading: boolean;
  tableInstance: React.MutableRefObject<GKDataGridInstance<ActivityListData> | null>;
};

export const ActivityDataGrid: React.FC<ActivityDataGridProps> = React.memo(
  ({ className, isLoading, tableInstance }) => {
    const {
      columns,
      searchResults: gridData,
      expanderHeader,
      expanderWidth,
      useGetRowProps,
      onSortChange,
      defaultSorted,
      rowDetail,
      hideActivityListDataGridCheckboxes,
      onExpandChange,
      onClick,
    } = useActivityDataGrid({ tableInstance });
    const onSelectRows = useSetAtom(SelectedActivityRowsAtom);
    const myRef = React.useRef<HTMLButtonElement | null>(null);

    let emptyView: ReactNode | undefined;

    if (isEmpty(gridData)) {
      emptyView = <EmptyActivities />;
    }
    // else if(areResultsEmpty) {
    //   emptyView = <EmptyResults />;
    // }

    const defaultSelected = React.useMemo(() => {
      // Workaround to maintain checkbox states
      const newDefaults: IRowIndicator = {};
      const selectedRows =
        tableInstance.current?.rows.filter((row) => row.isSelected) ?? [];
      for (const id of gridData
        .map((row, index) =>
          selectedRows.some(
            (selectedRow) => selectedRow.original.activityId === row.activityId
          )
            ? index
            : -1
        )
        .filter((f) => f >= 0)) {
        newDefaults[id] = true;
      }
      return newDefaults;
    }, [gridData, tableInstance]);

    return isLoading ? (
      <GKDataGrid
        columns={columns}
        data={gridData}
        onSelection={onSelectRows}
        ref={tableInstance}
        className={className}
      />
    ) : (
      <>
        <GKLayoutContainer>
          <GKContent className="overflow-auto">
            <GKDataGrid
              id={"activityListId"}
              columns={columns}
              data-qa="ActivitiesList_DataGrid"
              expanderHeader={expanderHeader}
              data={gridData}
              defaultSorted={defaultSorted}
              ref={tableInstance}
              onSelection={
                hideActivityListDataGridCheckboxes ? undefined : onSelectRows
              }
              onSortChange={onSortChange}
              expanderWidth={expanderWidth}
              rowDetail={rowDetail}
              rowProps={useGetRowProps}
              // Virtualization requires a fixed height so when there is no data the "Empty" components are shoved below the line
              virtualize={!isEmpty(gridData)}
              className="activity-table"
              defaultVirtualRowHeight={62}
              defaultSelected={defaultSelected}
              onExpandChange={onExpandChange}
            />
            {didUserScroll && (
              <>
                <button onClick={onClick} className="scrollToTop" ref={myRef}>
                  <GKIcon
                    className="scrollToTopIcon"
                    name={GKIconName.ArrowUpward}
                  />
                </button>
                <GKTooltip className="tooltip" target={myRef}>
                  <FormattedMessage id="scroll_to_top" />
                </GKTooltip>
              </>
            )}
          </GKContent>
        </GKLayoutContainer>
        {emptyView}
      </>
    );
  }
);
ActivityDataGrid.displayName = "ActivityDataGrid";
