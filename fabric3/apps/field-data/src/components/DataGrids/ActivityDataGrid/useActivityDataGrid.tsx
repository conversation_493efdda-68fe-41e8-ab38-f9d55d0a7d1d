import {
  DataGridColumn,
  DataGridRow,
  DataGridSortingRule,
  GKDataGridInstance,
  IGKDataGridProps,
  IRowIndicator,
  useDeviceSizes,
} from "@granular/gds";
import { useAtom, useAtomValue } from "jotai";
import { useUser, useUserLocale } from "@granular/fabric3-core";
import { useIntl } from "react-intl";
import React from "react";
import { segment } from "@granular/fabric3-core";
import { UseTableRowProps } from "react-table";
import { FieldDataQueryParams } from "@granular/fabric3-library-activities-components/src/types/shared";
import { SYSTEM_OF_MEASUREMENT } from "@granular/fabric3-library-activities-components/src/constants/unit";
import { getAreaUnit } from "@granular/fabric3-library-activities-components/src/helpers/area";
import { useFieldDataSearchParams } from "@granular/fabric3-library-activities-components/src/hooks/useFieldDataSearchParams";
import { useOperationIds } from "@granular/fabric3-library-activities-components/src/hooks/useOperationIds";
import { useFieldDataFeatureFlags } from "@granular/fabric3-library-activities-components/src/hooks/useFieldDataFeatureFlags";
import { useActivitySummaries } from "@granular/fabric3-library-activities-components/src/hooks/useActivitySummaries";
import { getInputIdsFromSummaries } from "@granular/fabric3-library-activities-components/src/hooks/loadFromRemote/loadSummariesFromRemote";
import { useFetchInputs } from "@granular/fabric3-library-activities-components/src/hooks/useFetchInputs";
import { INPUT_DATA_TYPES } from "@granular/fabric3-library-activities-components";
import clsx from "clsx";
import {
  ActiveListStateAtom,
  EditColumnsStateAtom,
  ExpandedUniqueRowIdsAtom,
  TableListSortAtom,
} from "../../../state/activitiesTable";
import { ActivityListData } from "../../../models/activitiesTable";
import { useActivityListData } from "../../../hooks/useActivityListData";
import { useActivityListDataFilters } from "../../../hooks/useActivityListDataFilters";
import { mobileRenderer } from "./components/mobileRenderer";
import { getActivityViewColumns, getExpanderHeader } from "./helpers";
import { ActivityViewDetails } from "./components/ActivityViewDetails/ActivityViewDetails";

type useActivityDataGridProp = {
  tableInstance: React.MutableRefObject<GKDataGridInstance<ActivityListData> | null>;
};

export type UseActivityDataGridResult = {
  columns: IGKDataGridProps<ActivityListData>["columns"];
  expanderHeader: IGKDataGridProps<ActivityListData>["expanderThPrependText"];
  expanderWidth: number;
  useGetRowProps: IGKDataGridProps<ActivityListData>["rowProps"];
  defaultSorted: Array<DataGridSortingRule<ActivityListData>>;
  onSortChange: (result: DataGridSortingRule<Record<string, boolean>>) => void;
  renderFunction:
    | undefined
    | ((tableInstance: GKDataGridInstance<ActivityListData>) => JSX.Element);
  rowDetail:
    | ((row: DataGridRow<ActivityListData>) => React.ReactNode)
    | undefined;
  hideActivityListDataGridCheckboxes?: boolean;
  onExpandChange: (expanded: IRowIndicator) => void;
  searchResults: ActivityListData[];
  onClick: () => void;
};

export type UseActivityDataGridHook = (
  prop: useActivityDataGridProp,
) => UseActivityDataGridResult; // props: UseActivityDataGridProps,

export const useActivityDataGrid: UseActivityDataGridHook = ({
  tableInstance,
}) => {
  const intl = useIntl();
  const { hideActivityListDataGridCheckboxes } = useFieldDataFeatureFlags();
  const { isMobileSized } = useDeviceSizes();
  const { data: ActivityListData } = useActivityListData();
  const [, setExpandedUniqueRowIds] = useAtom(ExpandedUniqueRowIdsAtom);
  const locale = useUserLocale();
  const [editColumnsSelectedOptions] = useAtom(EditColumnsStateAtom);
  const activeView = useAtomValue(ActiveListStateAtom);
  const [ActivityListSort, setActivityListSort] = useAtom(TableListSortAtom);
  const [{ businessCycleId: selectedBusinessCycleId }] =
    useFieldDataSearchParams<FieldDataQueryParams>();
  const operationIds = useOperationIds();

  const { data: activitySummaries } = useActivitySummaries(
    selectedBusinessCycleId,
  );
  const summaryByActivityId = React.useMemo(
    () => activitySummaries?.summaryByActivityId ?? {},
    [activitySummaries],
  );
  const inputIds = React.useMemo(
    () => getInputIdsFromSummaries(summaryByActivityId),
    [summaryByActivityId],
  );
  const { data: inputsById } = useFetchInputs(inputIds);
  const mixListIds: string[] = [];

  const mixtures = Object.values(inputsById ?? []).filter((x) =>
    [INPUT_DATA_TYPES.MIXTURE, INPUT_DATA_TYPES.CUSTOM_MIXTURE].includes(
      x?.data_type,
    ),
  );
  if (mixtures) {
    for (const mix of mixtures) {
      const components = mix.details?.components;
      if (components) {
        for (const component of components) {
          mixListIds.push(component.input_id);
        }
      }
    }
  }
  const { data: mixtureComponentInputsById } = useFetchInputs(mixListIds);

  const {
    data: { searchResults },
  } = useActivityListDataFilters(mixtureComponentInputsById);

  const {
    data: { preferredUnitSystem },
  } = useUser();

  const rowDetail = React.useCallback(
    (activityData: UseTableRowProps<ActivityListData>) => {
      return <ActivityViewDetails {...activityData.original} />;
    },
    [],
  );

  const expanderHeader: IGKDataGridProps<ActivityListData>["expanderThPrependText"] =
    React.useCallback(
      (allExpanded: boolean) => {
        return getExpanderHeader(intl, isMobileSized, allExpanded);
      },
      [isMobileSized, intl],
    );

  const expanderWidth = React.useMemo(
    () => (isMobileSized ? 50 : 150),
    [isMobileSized],
  );

  const onExpandChange = React.useCallback(
    (expanded: IRowIndicator) => {
      const expandedRowIds: string[] = [];
      for (const [key, value] of Object.entries(expanded)) {
        value && expandedRowIds.push(key);
      }
      const expandedRowUniqueIds: string[] = [];
      for (const [index, row] of ActivityListData.entries()) {
        if (expandedRowIds.includes(`${index}`)) {
          expandedRowUniqueIds.push(row.uniqueId!);
        }
      }
      // send a click 'Expand All/Collapse All' event to segment.
      // Only track if all rows are expanded or all are collapsed
      if (
        expandedRowIds.length === ActivityListData.length ||
        expandedRowIds.length === 0
      ) {
        segment.track(
          "click 'Expand All/Collapse All' rows within ActivityDataGrid - Field Data",
          {
            action:
              expandedRowIds.length === ActivityListData.length
                ? "expand_all"
                : "collapse_all",
          },
        );
      }

      setExpandedUniqueRowIds(expandedRowUniqueIds);
    },
    [ActivityListData, setExpandedUniqueRowIds],
  );

  const renderFunction = React.useMemo(
    () => (isMobileSized ? mobileRenderer : undefined),
    [isMobileSized],
  );

  const areaUnit = React.useMemo(() => {
    const [areaUnit] = getAreaUnit(
      preferredUnitSystem as SYSTEM_OF_MEASUREMENT,
    );
    return areaUnit;
  }, [preferredUnitSystem]);

  const columns: Array<DataGridColumn<ActivityListData>> = React.useMemo(() => {
    return getActivityViewColumns(
      areaUnit,
      locale,
      editColumnsSelectedOptions[activeView],
      isMobileSized,
    );
  }, [areaUnit, locale, editColumnsSelectedOptions, activeView, isMobileSized]);

  const onSortChange = React.useCallback(
    (result: DataGridSortingRule<Record<string, boolean>>) => {
      if (
        result.id !== ActivityListSort[0]?.id ||
        result.desc !== ActivityListSort[0]?.desc
      ) {
        setActivityListSort([result]);
        const key = `ActivityTableScroll_${operationIds[0]}_${selectedBusinessCycleId}`;
        sessionStorage.removeItem(key);
      }
    },
    [
      ActivityListSort,
      setActivityListSort,
      operationIds,
      selectedBusinessCycleId,
    ],
  );

  const onClick = React.useCallback(() => {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access
    tableInstance.current?.virtualList?.scrollToItem(0, "start");
  }, [tableInstance]);

  return {
    columns,
    searchResults,
    expanderHeader,
    expanderWidth,
    useGetRowProps,
    renderFunction,
    onSortChange,
    defaultSorted: ActivityListSort,
    rowDetail,
    hideActivityListDataGridCheckboxes,
    onExpandChange,
    onClick,
  };
};

const useGetRowProps: IGKDataGridProps<ActivityListData>["rowProps"] = (
  row,
) => {
  const data = row.original;
  const emptyExpandedContent =
    !data.plannedResourceSummaries?.length &&
    !data.completedResourceSummaries?.length;
  return {
    className: clsx({
      emptyExpandedContent,
    }),
    "data-qa": `ActivityViewRow_${data.activityId}`,
  };
};
