import * as React from "react";
import { FormattedMessage, IntlShape } from "react-intl";
import {
  ActiveItem,
  Filter,
  FilterItem,
  GroupedDataWithActive,
} from "@granular/fabric3-library-activities-components/src/types/filters";
import { GKFormGroup, GKInput, GKInputGroup, GKLabel } from "@granular/gds";
import { AdditionalFilterMultiSelect } from "./fieldListFilters";

const generatePillText = (selectedItems: FilterItem[]) => {
  return selectedItems.map((item) => item.label).join(", ");
};

const getFilterFieldList = (
  filterData: GroupedDataWithActive,
  updatedFilter: Filter,
  setUpdatedFilter: React.Dispatch<React.SetStateAction<Filter>>,
  appliedFilter: Filter,
  intl: IntlShape,
  initialStartDate: string,
  initialEndDate: string,
) => {
  const updateFilterValues = (selectedItems: ActiveItem[], id: string): void =>
    setUpdatedFilter({
      ...updatedFilter,
      [id]: selectedItems.map((item) => item),
    });

  const updateDateRange = (selectedItem: string, id: string): void => {
    setUpdatedFilter({
      ...updatedFilter,
      [id]: selectedItem,
    });
  };

  return [
    {
      id: "a-activityTypes",
      name: intl.formatMessage({ id: "activities_activityType" }),
      data: [],
      ui: (
        <div key={"2"}>
          <AdditionalFilterMultiSelect
            data={filterData.activityTypes.sort((a, b) =>
              a.label?.localeCompare(b.label),
            )}
            dataQA="FiltersModal_SelectActivityType"
            label={intl.formatMessage({
              id: "activities_activityType",
            })}
            placeholder={intl.formatMessage({ id: "allActivities" })}
            onUpdateSelection={(selectedItems: ActiveItem[]) =>
              updateFilterValues(selectedItems, "activityTypes")
            }
            onClearSelection={() => updateFilterValues([], "activityTypes")}
          />
        </div>
      ),
      isChanged: updatedFilter.activityTypes.length > 0,
      isGrouped: false,
      currentValue: generatePillText(appliedFilter.activityTypes),
    },
    {
      id: "b-crops",
      name: intl.formatMessage({ id: "Crops" }),
      data: [],
      ui: (
        <div key={"1"}>
          <AdditionalFilterMultiSelect
            data={filterData.crops.sort((a, b) =>
              a.label?.localeCompare(b.label),
            )}
            dataQA="FiltersModal_SelectCrops"
            label={intl.formatMessage({
              id: "Crops",
            })}
            id="crops"
            placeholder={intl.formatMessage({ id: "activities_selectCrops" })}
            onUpdateSelection={(selectedItems: ActiveItem[]) =>
              updateFilterValues(selectedItems, "crops")
            }
            onClearSelection={() => updateFilterValues([], "crops")}
          />
        </div>
      ),
      isChanged: updatedFilter.crops.length > 0,
      isGrouped: false,
      currentValue: generatePillText(appliedFilter.crops),
    },

    {
      id: "c-dataTypes",
      name: intl.formatMessage({ id: "activities_dataType" }),
      data: [],
      ui: (
        <div key={"3"}>
          <AdditionalFilterMultiSelect
            data={filterData.dataTypes.sort((a, b) =>
              a.label?.localeCompare(b.label),
            )}
            dataQA="FiltersModal_SelectDataType"
            label={intl.formatMessage({ id: "activities_dataType" })}
            placeholder={intl.formatMessage({
              id: "activities_fieldData_filters_allDataTypes",
            })}
            onUpdateSelection={(selectedItems: ActiveItem[]) =>
              updateFilterValues(selectedItems, "dataTypes")
            }
            onClearSelection={() => updateFilterValues([], "dataTypes")}
          />
        </div>
      ),
      isChanged: updatedFilter.dataTypes.length > 0,
      isGrouped: false,
      currentValue: generatePillText(appliedFilter.dataTypes),
    },
    {
      id: "d-farms",
      name: intl.formatMessage({ id: "common_farms" }),
      data: [],
      ui: (
        <div key={"4"}>
          <AdditionalFilterMultiSelect
            data={filterData.farms.sort((a, b) =>
              a.label?.localeCompare(b.label),
            )}
            dataQA="FiltersModal_SelectFarms"
            label={intl.formatMessage({ id: "common_farms" })}
            placeholder={intl.formatMessage({ id: "activities_allFarms" })}
            onUpdateSelection={(selectedItems: ActiveItem[]) =>
              updateFilterValues(selectedItems, "farms")
            }
            onClearSelection={() => updateFilterValues([], "farms")}
          />
        </div>
      ),
      isChanged: updatedFilter.farms.length > 0,
      isGrouped: false,
      currentValue: generatePillText(appliedFilter.farms),
    },
    {
      id: "e-fields",
      name: intl.formatMessage({ id: "common_fields" }),
      data: [],
      ui: (
        <div key={"5"}>
          <AdditionalFilterMultiSelect
            data={filterData.fields.sort((a, b) =>
              a.label?.localeCompare(b.label),
            )}
            dataQA="FiltersModal_Selectfields"
            label={intl.formatMessage({ id: "common_fields" })}
            placeholder={intl.formatMessage({ id: "activities_allFields" })}
            onUpdateSelection={(selectedItems: ActiveItem[]) =>
              updateFilterValues(selectedItems, "fields")
            }
            onClearSelection={() => updateFilterValues([], "fields")}
            showArea={true}
          />
        </div>
      ),
      isChanged: updatedFilter.fields?.length > 0,
      isGrouped: false,
      currentValue: generatePillText(appliedFilter.fields),
    },
    {
      id: "f-products",
      name: intl.formatMessage({ id: "all_products" }),
      data: [],
      ui: (
        <div key={"6"}>
          <AdditionalFilterMultiSelect
            data={filterData.products.sort((a, b) =>
              a.label?.localeCompare(b.label),
            )}
            dataQA="FiltersModal_SelectProducts"
            label={intl.formatMessage(
              { id: "Product" },
              { Product: filterData.products.length },
            )}
            placeholder={intl.formatMessage({ id: "all_products" })}
            onUpdateSelection={(selectedItems: ActiveItem[]) =>
              updateFilterValues(selectedItems, "products")
            }
            onClearSelection={() => updateFilterValues([], "products")}
            useItemTemplate={true}
          />
        </div>
      ),
      isChanged: updatedFilter.products?.length > 0,
      isGrouped: false,
      currentValue: generatePillText(appliedFilter.products),
    },
    {
      id: "g-monitors",
      name: intl.formatMessage({ id: "common_monitors" }),
      data: [],
      ui: (
        <div key={"8"}>
          <AdditionalFilterMultiSelect
            data={filterData.monitors.sort((a, b) =>
              a.label?.localeCompare(b.label),
            )}
            dataQA="FiltersModal_SelectMonitors"
            label={intl.formatMessage({ id: "common_monitors" })}
            placeholder={intl.formatMessage({
              id: "activityList_filter_allMonitors",
            })}
            onUpdateSelection={(selectedItems) =>
              updateFilterValues(selectedItems, "monitors")
            }
            onClearSelection={() => updateFilterValues([], "monitors")}
          />
        </div>
      ),
      isChanged: updatedFilter.monitors.length > 0,
      isGrouped: false,
      currentValue: generatePillText(appliedFilter.monitors),
    },
    {
      id: "h-sources",
      name: intl.formatMessage({ id: "Sources" }),
      data: [],
      ui: (
        <div key={"7"}>
          <AdditionalFilterMultiSelect
            data={filterData.sources.sort((a, b) =>
              a.label?.localeCompare(b.label),
            )}
            dataQA="FiltersModal_SelectSources"
            label={intl.formatMessage({ id: "Sources" })}
            placeholder={intl.formatMessage({
              id: "activityList_filter_allSources",
            })}
            onUpdateSelection={(selectedItems: ActiveItem[]) =>
              updateFilterValues(selectedItems, "sources")
            }
            onClearSelection={() => updateFilterValues([], "sources")}
          />
        </div>
      ),
      isChanged: updatedFilter.sources.length > 0,
      isGrouped: false,
      currentValue: generatePillText(appliedFilter.sources),
    },
    {
      id: "i-startDateRange",
      name: intl.formatMessage({ id: "start_date_range" }),
      data: [],
      ui: (
        <div key={"9"}>
          <GKFormGroup className="mb-0 mt-2">
            <GKLabel>
              <FormattedMessage id="start_date_range" />
            </GKLabel>
            <GKInputGroup>
              <GKInput
                type="date"
                data-qa="fieldList-dateRangeStart"
                value={updatedFilter.startDate}
                min={initialStartDate}
                max={appliedFilter.endDate}
                onChange={(e) => updateDateRange(e.target.value, "startDate")}
                onKeyDown={(event: React.KeyboardEvent<HTMLInputElement>) =>
                  event.preventDefault()
                }
                onClick={(event: React.MouseEvent<HTMLInputElement>) =>
                  (event.target as HTMLInputElement).showPicker()
                }
              />
            </GKInputGroup>
          </GKFormGroup>
        </div>
      ),
      isChanged: updatedFilter.startDate.length > 0,
      isGrouped: false,
      currentValue: `${updatedFilter.startDate}`,
      getDisplayName: () => {
        return `${updatedFilter.startDate}`;
      },
    },
    {
      id: "j-endDateRange",
      name: intl.formatMessage({ id: "end_date_range" }),
      data: [],
      ui: (
        <div key={"10"}>
          <GKFormGroup className="mb-0 mt-2">
            <GKLabel>
              <FormattedMessage id="end_date_range" />
            </GKLabel>
            <GKInputGroup>
              <GKInput
                type="date"
                data-qa="fieldList-dateRangeEnd"
                value={updatedFilter.endDate}
                min={appliedFilter.startDate}
                max={initialEndDate}
                onChange={(e) => updateDateRange(e.target.value, "endDate")}
                onKeyDown={(event: React.KeyboardEvent<HTMLInputElement>) =>
                  event.preventDefault()
                }
                onClick={(event: React.MouseEvent<HTMLInputElement>) =>
                  (event.target as HTMLInputElement).showPicker()
                }
              />
            </GKInputGroup>
          </GKFormGroup>
        </div>
      ),
      isChanged: updatedFilter.endDate.length > 0,
      isGrouped: false,
      currentValue: `${updatedFilter.endDate}`,
      getDisplayName: () => {
        return `${updatedFilter.endDate}`;
      },
    },
  ];
};
export default getFilterFieldList;
